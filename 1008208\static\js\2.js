(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[2],{

/***/ "./src/const.js":
/*!**********************!*\
  !*** ./src/const.js ***!
  \**********************/
/*! exports provided: userType, userIndustry, industry, assessMode, electrClassify, depthAdjustment, timeFrame, voltageClasses, baseVolt, noticeTime, electricityClassificationList, electricityPriceStrategyList, voltageGradeList */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"userType\", function() { return userType; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"userIndustry\", function() { return userIndustry; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"industry\", function() { return industry; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"assessMode\", function() { return assessMode; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"electrClassify\", function() { return electrClassify; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"depthAdjustment\", function() { return depthAdjustment; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"timeFrame\", function() { return timeFrame; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"voltageClasses\", function() { return voltageClasses; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"baseVolt\", function() { return baseVolt; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"noticeTime\", function() { return noticeTime; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"electricityClassificationList\", function() { return electricityClassificationList; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"electricityPriceStrategyList\", function() { return electricityPriceStrategyList; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"voltageGradeList\", function() { return voltageGradeList; });\n// 用户类别\nvar userType = [{\n  name: '商业用户',\n  value: '01'\n}, {\n  name: '工业用户',\n  value: '02'\n}\n// { name: '其他', value: '03'}\n];\n// 用户行业\nvar userIndustry = [{\n  name: '钢铁行业',\n  value: '01'\n}, {\n  name: '机械制造行业',\n  value: '02'\n}, {\n  name: '纺织行业',\n  value: '03'\n}, {\n  name: '化工行业',\n  value: '04'\n}, {\n  name: '有色金属行业',\n  value: '05'\n}, {\n  name: '建材行业',\n  value: '06'\n}, {\n  name: '其他',\n  value: '07'\n}];\n// 行业编码\nvar industry = [{\n  name: '黑色金属冶炼和压延加工业',\n  value: '3200',\n  industryType: '01'\n}, {\n  name: '炼铁',\n  value: '3210',\n  industryType: '01'\n}, {\n  name: '炼钢',\n  value: '3220',\n  industryType: '01'\n}, {\n  name: '钢压延加工',\n  value: '3230',\n  industryType: '01'\n}, {\n  name: '铁合金冶炼',\n  value: '3240',\n  industryType: '01'\n}, {\n  name: '通用设备制造业',\n  value: '3500',\n  industryType: '02'\n}, {\n  name: '锅炉及原动设备制造',\n  value: '3510',\n  industryType: '02'\n}, {\n  name: '风能原动设备制造',\n  value: '3511',\n  industryType: '02'\n}, {\n  name: '锅炉及辅助设备、其他原动设备制造',\n  value: '3512',\n  industryType: '02'\n}, {\n  name: '金属加工机械制造',\n  value: '3520',\n  industryType: '02'\n}, {\n  name: '物料搬运设备制造',\n  value: '3530',\n  industryType: '02'\n}, {\n  name: '泵、阀门、压缩机及类似机械制造',\n  value: '3540',\n  industryType: '02'\n}, {\n  name: '轴承、齿轮和传动部件制造',\n  value: '3550',\n  industryType: '02'\n}, {\n  name: '烘炉、风机、包装等设备制造',\n  value: '3560',\n  industryType: '02'\n}, {\n  name: '通用零部件制造',\n  value: '3580',\n  industryType: '02'\n}, {\n  name: '文化、办公用机械制造',\n  value: '35A0',\n  industryType: '02'\n}, {\n  name: '其他通用设备制造业',\n  value: '35B0',\n  industryType: '02'\n}, {\n  name: '工业机器人制造',\n  value: '35B1',\n  industryType: '02'\n}, {\n  name: '特殊作业机器人制造',\n  value: '35B2',\n  industryType: '02'\n}, {\n  name: '增材制造装备制造',\n  value: '35B3',\n  industryType: '02'\n}, {\n  name: '其他未列明通用设备制造业',\n  value: '35B9',\n  industryType: '02'\n}, {\n  name: '专用设备制造业',\n  value: '3600',\n  industryType: '02'\n}, {\n  name: '采矿、冶金、建筑专用设备制造',\n  value: '3610',\n  industryType: '02'\n}, {\n  name: '化工、木材、非金属加工专用设备制造',\n  value: '3620',\n  industryType: '02'\n}, {\n  name: '食品、饮料、烟草及饲料生产专用设备制造',\n  value: '3630',\n  industryType: '02'\n}, {\n  name: '印刷、制药、日化及日用品生产专用设备制造',\n  value: '3640',\n  industryType: '02'\n}, {\n  name: '纺织、服装和皮革工业专用设备制造',\n  value: '3650',\n  industryType: '02'\n}, {\n  name: '电子和电工机械专用设备制造',\n  value: '3660',\n  industryType: '02'\n}, {\n  name: '农、林、牧、渔专用机械制造',\n  value: '3670',\n  industryType: '02'\n}, {\n  name: '医疗仪器设备及器械制造',\n  value: '3680',\n  industryType: '02'\n}, {\n  name: '环保、邮政、社会公共服务及其他专用设备制造',\n  value: '3690',\n  industryType: '02'\n}, {\n  name: '铁路、船舶、航空航天和其他运输设备制造业',\n  value: '3700',\n  industryType: '02'\n}, {\n  name: '铁路运输设备制造',\n  value: '3710',\n  industryType: '02'\n}, {\n  name: '摩托车制造',\n  value: '3730',\n  industryType: '02'\n}, {\n  name: '自行车和残疾人座车制造',\n  value: '3740',\n  industryType: '02'\n}, {\n  name: '船舶及相关装置制造',\n  value: '3750',\n  industryType: '02'\n}, {\n  name: '航空、航天器及设备制造',\n  value: '3760',\n  industryType: '02'\n}, {\n  name: '城市轨道交通设备制造',\n  value: '3770',\n  industryType: '02'\n}, {\n  name: '助动车制造',\n  value: '3780',\n  industryType: '02'\n}, {\n  name: '交通器材及其他交通运输设备制造',\n  value: '3790',\n  industryType: '02'\n}, {\n  name: '非公路休闲车及零配件制造',\n  value: '37A0',\n  industryType: '02'\n}, {\n  name: '潜水救捞及其他未列明运输设备制造',\n  value: '37B0',\n  industryType: '02'\n}, {\n  name: '汽车制造业',\n  value: '3800',\n  industryType: '02'\n}, {\n  name: '汽车整车制造',\n  value: '3810',\n  industryType: '02'\n}, {\n  name: '汽柴油车整车制造',\n  value: '3811',\n  industryType: '02'\n}, {\n  name: '新能源车整车制造',\n  value: '3812',\n  industryType: '02'\n}, {\n  name: '汽车用发动机制造',\n  value: '3820',\n  industryType: '02'\n}, {\n  name: '改装汽车制造',\n  value: '3830',\n  industryType: '02'\n}, {\n  name: '低速汽车制造',\n  value: '3840',\n  industryType: '02'\n}, {\n  name: '电车制造',\n  value: '3850',\n  industryType: '02'\n}, {\n  name: '汽车车身、挂车制造',\n  value: '3860',\n  industryType: '02'\n}, {\n  name: '汽车零部件及配件制造',\n  value: '3870',\n  industryType: '02'\n}, {\n  name: '电气机械和器材制造业',\n  value: '3900',\n  industryType: '02'\n}, {\n  name: '电机制造',\n  value: '3910',\n  industryType: '02'\n}, {\n  name: '输配电及控制设备制造',\n  value: '3920',\n  industryType: '02'\n}, {\n  name: '光伏设备及元器件制造',\n  value: '3921',\n  industryType: '02'\n}, {\n  name: '其他输配电及控制设备制造',\n  value: '3929',\n  industryType: '02'\n}, {\n  name: '电线、电缆、光缆及电工器材制造',\n  value: '3930',\n  industryType: '02'\n}, {\n  name: '电池制造',\n  value: '3940',\n  industryType: '02'\n}, {\n  name: '家用电力器具制造',\n  value: '3950',\n  industryType: '02'\n}, {\n  name: '非电力家用器具制造',\n  value: '3960',\n  industryType: '02'\n}, {\n  name: '照明器具制造',\n  value: '3970',\n  industryType: '02'\n}, {\n  name: '其他电气机械及器材制造',\n  value: '3990',\n  industryType: '02'\n}, {\n  name: '计算机、通信和其他电子设备制造业',\n  value: '4000',\n  industryType: '02'\n}, {\n  name: '通信设备制造',\n  value: '4010',\n  industryType: '02'\n}, {\n  name: '雷达及配套设备制造',\n  value: '4020',\n  industryType: '02'\n}, {\n  name: '广播电视设备制造',\n  value: '4030',\n  industryType: '02'\n}, {\n  name: '计算机制造',\n  value: '4040',\n  industryType: '02'\n}, {\n  name: '电子器件制造',\n  value: '4050',\n  industryType: '02'\n}, {\n  name: '电子元件及电子专用材料制造',\n  value: '4060',\n  industryType: '02'\n}, {\n  name: '非专业视听设备制造',\n  value: '4070',\n  industryType: '02'\n}, {\n  name: '智能消费设备制造',\n  value: '4080',\n  industryType: '02'\n}, {\n  name: '服务消费机器人制造',\n  value: '4081',\n  industryType: '02'\n}, {\n  name: '智能无人飞行器制造',\n  value: '4082',\n  industryType: '02'\n}, {\n  name: '可穿戴智能设备制造',\n  value: '4083',\n  industryType: '02'\n}, {\n  name: '智能车载设备制造',\n  value: '4084',\n  industryType: '02'\n}, {\n  name: '其他智能消费设备制造',\n  value: '4085',\n  industryType: '02'\n}, {\n  name: '其他电子设备制造',\n  value: '4090',\n  industryType: '02'\n}, {\n  name: '仪器仪表制造业',\n  value: '4100',\n  industryType: '02'\n}, {\n  name: '通用仪器仪表制造',\n  value: '4110',\n  industryType: '02'\n}, {\n  name: '专用仪器仪表制造',\n  value: '4120',\n  industryType: '02'\n}, {\n  name: '钟表与计时仪器制造',\n  value: '4130',\n  industryType: '02'\n}, {\n  name: '光学仪器制造',\n  value: '4140',\n  industryType: '02'\n}, {\n  name: '光学仪器制造',\n  value: '4141',\n  industryType: '02'\n}, {\n  name: '衡器制造',\n  value: '4160',\n  industryType: '02'\n}, {\n  name: '其他仪器仪表的制造',\n  value: '4190',\n  industryType: '02'\n}, {\n  name: '纺织业',\n  value: '1700',\n  industryType: '03'\n}, {\n  name: '棉纺织及印染精加工',\n  value: '1710',\n  industryType: '03'\n}, {\n  name: '毛纺织和染整精加工',\n  value: '1720',\n  industryType: '03'\n}, {\n  name: '麻纺织和染整精加工',\n  value: '1730',\n  industryType: '03'\n}, {\n  name: '丝绢纺织及印染精加工',\n  value: '1740',\n  industryType: '03'\n}, {\n  name: '家用纺织制成品制造',\n  value: '1750',\n  industryType: '03'\n}, {\n  name: '针织或钩针编织物及其制品制造',\n  value: '1760',\n  industryType: '03'\n}, {\n  name: '化纤织造及印染精加工',\n  value: '1770',\n  industryType: '03'\n}, {\n  name: '产业用纺织制成品制造',\n  value: '1780',\n  industryType: '03'\n}, {\n  name: '纺织服装、服饰业',\n  value: '1800',\n  industryType: '03'\n}, {\n  name: '机织服装制造',\n  value: '1810',\n  industryType: '03'\n}, {\n  name: '针织或钩针编织服装制造',\n  value: '1840',\n  industryType: '03'\n}, {\n  name: '服饰制造',\n  value: '1850',\n  industryType: '03'\n}, {\n  name: '石油、煤炭及其他燃料加工业',\n  value: '2500',\n  industryType: '04'\n}, {\n  name: '精炼石油产品制造',\n  value: '2510',\n  industryType: '04'\n}, {\n  name: '核燃料加工',\n  value: '2530',\n  industryType: '04'\n}, {\n  name: '煤炭加工',\n  value: '2540',\n  industryType: '04'\n}, {\n  name: '煤化工',\n  value: '2541',\n  industryType: '04'\n}, {\n  name: '煤制品制造',\n  value: '2542',\n  industryType: '04'\n}, {\n  name: '生物质燃料加工',\n  value: '2550',\n  industryType: '04'\n}, {\n  name: '化学原料和化学制品制造业',\n  value: '2600',\n  industryType: '04'\n}, {\n  name: '基础化学原料制造',\n  value: '2610',\n  industryType: '04'\n}, {\n  name: '肥料制造',\n  value: '2620',\n  industryType: '04'\n}, {\n  name: '氮肥制造',\n  value: '2621',\n  industryType: '04'\n}, {\n  name: '磷肥制造',\n  value: '2622',\n  industryType: '04'\n}, {\n  name: '钾肥制造',\n  value: '2623',\n  industryType: '04'\n}, {\n  name: '复混肥料制造',\n  value: '2624',\n  industryType: '04'\n}, {\n  name: '有机肥料及微生物肥料制造',\n  value: '2625',\n  industryType: '04'\n}, {\n  name: '其他肥料制造',\n  value: '2629',\n  industryType: '04'\n}, {\n  name: '农药制造',\n  value: '2630',\n  industryType: '04'\n}, {\n  name: '化学农药制造',\n  value: '2631',\n  industryType: '04'\n}, {\n  name: '生物化学农药及微生物农药制造',\n  value: '2632',\n  industryType: '04'\n}, {\n  name: '涂料、油墨、颜料及类似产品制造',\n  value: '2640',\n  industryType: '04'\n}, {\n  name: '合成材料制造',\n  value: '2650',\n  industryType: '04'\n}, {\n  name: '专用化学产品制造',\n  value: '2660',\n  industryType: '04'\n}, {\n  name: '日用化学产品制造',\n  value: '2670',\n  industryType: '04'\n}, {\n  name: '氯碱',\n  value: '2690',\n  industryType: '04'\n}, {\n  name: '电石',\n  value: '26A0',\n  industryType: '04'\n}, {\n  name: '黄磷',\n  value: '26B0',\n  industryType: '04'\n}, {\n  name: '炸药、火工及焰火产品制造',\n  value: '26D0',\n  industryType: '04'\n}, {\n  name: '化学纤维制造业',\n  value: '2800',\n  industryType: '04'\n}, {\n  name: '纤维素纤维原料及纤维制造',\n  value: '2810',\n  industryType: '04'\n}, {\n  name: '合成纤维制造',\n  value: '2820',\n  industryType: '04'\n}, {\n  name: '生物基材料制造',\n  value: '2830',\n  industryType: '04'\n}, {\n  name: '有色金属矿采选业',\n  value: '0900',\n  industryType: '05'\n}, {\n  name: '常用有色金属矿采选',\n  value: '0910',\n  industryType: '05'\n}, {\n  name: '贵金属矿采选',\n  value: '0920',\n  industryType: '05'\n}, {\n  name: '稀有稀土金属矿采选',\n  value: '0930',\n  industryType: '05'\n}, {\n  name: '有色金属冶炼和压延加工业',\n  value: '3300',\n  industryType: '05'\n}, {\n  name: '常用有色金属冶炼',\n  value: '3310',\n  industryType: '05'\n}, {\n  name: '铝冶炼',\n  value: '3316',\n  industryType: '05'\n}, {\n  name: '铅锌冶炼',\n  value: '3317',\n  industryType: '05'\n}, {\n  name: '其他常用有色金属冶炼',\n  value: '3319',\n  industryType: '05'\n}, {\n  name: '贵金属冶炼',\n  value: '3320',\n  industryType: '05'\n}, {\n  name: '稀有稀土金属冶炼',\n  value: '3330',\n  industryType: '05'\n}, {\n  name: '有色金属合金制造',\n  value: '3340',\n  industryType: '05'\n}, {\n  name: '有色金属压延加工',\n  value: '3350',\n  industryType: '05'\n}, {\n  name: '水泥制造',\n  value: '3111',\n  industryType: '06'\n}, {\n  name: '水泥、石灰和石膏制造',\n  value: '3110',\n  industryType: '06'\n}, {\n  name: '煤炭开采和洗选业',\n  value: '0600',\n  industryType: '07'\n}, {\n  name: '烟煤和无烟煤开采洗选',\n  value: '0610',\n  industryType: '07'\n}, {\n  name: '褐煤开采洗选',\n  value: '0620',\n  industryType: '07'\n}, {\n  name: '其他煤炭采选',\n  value: '0690',\n  industryType: '07'\n}, {\n  name: '石油和天然气开采业',\n  value: '0700',\n  industryType: '07'\n}, {\n  name: '石油开采',\n  value: '0710',\n  industryType: '07'\n}, {\n  name: '天然气开采',\n  value: '0720',\n  industryType: '07'\n}, {\n  name: '黑色金属矿采选业',\n  value: '0800',\n  industryType: '07'\n}, {\n  name: '铁矿采选',\n  value: '0810',\n  industryType: '07'\n}, {\n  name: '锰矿、铬矿采选',\n  value: '0820',\n  industryType: '07'\n}, {\n  name: '其他黑色金属矿采选',\n  value: '0890',\n  industryType: '07'\n}, {\n  name: '有色金属矿采选业',\n  value: '0900',\n  industryType: '07'\n}, {\n  name: '常用有色金属矿采选',\n  value: '0910',\n  industryType: '07'\n}, {\n  name: '贵金属矿采选',\n  value: '0920',\n  industryType: '07'\n}, {\n  name: '稀有稀土金属矿采选',\n  value: '0930',\n  industryType: '07'\n}, {\n  name: '非金属矿采选业',\n  value: '1000',\n  industryType: '07'\n}, {\n  name: '土砂石开采',\n  value: '1010',\n  industryType: '07'\n}, {\n  name: '化学矿采选',\n  value: '1020',\n  industryType: '07'\n}, {\n  name: '采盐',\n  value: '1030',\n  industryType: '07'\n}, {\n  name: '石棉及其他非金属矿采选',\n  value: '1090',\n  industryType: '07'\n}, {\n  name: '其他采矿业',\n  value: '1100',\n  industryType: '07'\n}, {\n  name: '与石油和天然气开采有关的服务活动',\n  value: '1110',\n  industryType: '07'\n}, {\n  name: '煤炭开采和洗选专业及辅助性活动',\n  value: '1120',\n  industryType: '07'\n}, {\n  name: '其他开采专业及辅助行活动',\n  value: '1130',\n  industryType: '07'\n}, {\n  name: '其他采矿业',\n  value: '1190',\n  industryType: '07'\n}, {\n  name: '农副食品加工业',\n  value: '1300',\n  industryType: '07'\n}, {\n  name: '谷物磨制',\n  value: '1310',\n  industryType: '07'\n}, {\n  name: '饲料加工',\n  value: '1320',\n  industryType: '07'\n}, {\n  name: '植物油加工',\n  value: '1330',\n  industryType: '07'\n}, {\n  name: '制糖业',\n  value: '1340',\n  industryType: '07'\n}, {\n  name: '屠宰及肉类加工',\n  value: '1350',\n  industryType: '07'\n}, {\n  name: '水产品加工',\n  value: '1360',\n  industryType: '07'\n}, {\n  name: '蔬菜、菌类、水果和坚果加工',\n  value: '1370',\n  industryType: '07'\n}, {\n  name: '其他农副食品加工',\n  value: '1390',\n  industryType: '07'\n}, {\n  name: '食品制造业',\n  value: '1400',\n  industryType: '07'\n}, {\n  name: '焙烤食品制造',\n  value: '1410',\n  industryType: '07'\n}, {\n  name: '糖果、巧克力及蜜饯制造',\n  value: '1420',\n  industryType: '07'\n}, {\n  name: '方便食品制造',\n  value: '1430',\n  industryType: '07'\n}, {\n  name: '乳制品制造',\n  value: '1440',\n  industryType: '07'\n}, {\n  name: '罐头食品制造',\n  value: '1450',\n  industryType: '07'\n}, {\n  name: '调味品、发酵制品制造',\n  value: '1460',\n  industryType: '07'\n}, {\n  name: '其他食品制造',\n  value: '1490',\n  industryType: '07'\n}, {\n  name: '酒、饮料及精制茶制造业',\n  value: '1500',\n  industryType: '07'\n}, {\n  name: '酒的制造',\n  value: '1520',\n  industryType: '07'\n}, {\n  name: '饮料制造',\n  value: '1530',\n  industryType: '07'\n}, {\n  name: '精制茶加工',\n  value: '1540',\n  industryType: '07'\n}, {\n  name: '烟草制品业',\n  value: '1600',\n  industryType: '07'\n}, {\n  name: '烟叶复烤',\n  value: '1610',\n  industryType: '07'\n}, {\n  name: '卷烟制造',\n  value: '1620',\n  industryType: '07'\n}, {\n  name: '其他烟草制品制造',\n  value: '1690',\n  industryType: '07'\n}, {\n  name: '纺织业',\n  value: '1700',\n  industryType: '07'\n}, {\n  name: '棉纺织及印染精加工',\n  value: '1710',\n  industryType: '07'\n}, {\n  name: '毛纺织和染整精加工',\n  value: '1720',\n  industryType: '07'\n}, {\n  name: '麻纺织和染整精加工',\n  value: '1730',\n  industryType: '07'\n}, {\n  name: '丝绢纺织及印染精加工',\n  value: '1740',\n  industryType: '07'\n}, {\n  name: '家用纺织制成品制造',\n  value: '1750',\n  industryType: '07'\n}, {\n  name: '针织或钩针编织物及其制品制造',\n  value: '1760',\n  industryType: '07'\n}, {\n  name: '化纤织造及印染精加工',\n  value: '1770',\n  industryType: '07'\n}, {\n  name: '产业用纺织制成品制造',\n  value: '1780',\n  industryType: '07'\n}, {\n  name: '纺织服装、服饰业',\n  value: '1800',\n  industryType: '07'\n}, {\n  name: '机织服装制造',\n  value: '1810',\n  industryType: '07'\n}, {\n  name: '针织或钩针编织服装制造',\n  value: '1840',\n  industryType: '07'\n}, {\n  name: '服饰制造',\n  value: '1850',\n  industryType: '07'\n}, {\n  name: '皮革、毛皮、羽毛及其制品和制鞋业',\n  value: '1900',\n  industryType: '07'\n}, {\n  name: '皮革鞣制加工',\n  value: '1910',\n  industryType: '07'\n}, {\n  name: '皮革制品制造',\n  value: '1920',\n  industryType: '07'\n}, {\n  name: '毛皮鞣制及制品加工',\n  value: '1930',\n  industryType: '07'\n}, {\n  name: '羽毛(绒)加工及制品制造',\n  value: '1940',\n  industryType: '07'\n}, {\n  name: '制鞋业',\n  value: '1950',\n  industryType: '07'\n}, {\n  name: '木材加工和木、竹、藤、棕、草制品业',\n  value: '2000',\n  industryType: '07'\n}, {\n  name: '木材加工',\n  value: '2010',\n  industryType: '07'\n}, {\n  name: '人造板制造',\n  value: '2020',\n  industryType: '07'\n}, {\n  name: '木质制品制造',\n  value: '2030',\n  industryType: '07'\n}, {\n  name: '建筑用木料及木材组件加工',\n  value: '2031',\n  industryType: '07'\n}, {\n  name: '木容器制造',\n  value: '2032',\n  industryType: '07'\n}, {\n  name: '软木制品及其他木制品制造',\n  value: '2039',\n  industryType: '07'\n}, {\n  name: '竹、藤、棕、草等制品制造',\n  value: '2040',\n  industryType: '07'\n}, {\n  name: '家具制造业',\n  value: '2100',\n  industryType: '07'\n}, {\n  name: '木质家具制造',\n  value: '2110',\n  industryType: '07'\n}, {\n  name: '竹、藤家具制造',\n  value: '2120',\n  industryType: '07'\n}, {\n  name: '金属家具制造',\n  value: '2130',\n  industryType: '07'\n}, {\n  name: '塑料家具制造',\n  value: '2140',\n  industryType: '07'\n}, {\n  name: '其他家具制造',\n  value: '2190',\n  industryType: '07'\n}, {\n  name: '造纸和纸制品业',\n  value: '2200',\n  industryType: '07'\n}, {\n  name: '纸浆制造',\n  value: '2210',\n  industryType: '07'\n}, {\n  name: '造纸',\n  value: '2220',\n  industryType: '07'\n}, {\n  name: '纸制品制造',\n  value: '2230',\n  industryType: '07'\n}, {\n  name: '印刷和记录媒介复制业',\n  value: '2300',\n  industryType: '07'\n}, {\n  name: '印刷',\n  value: '2310',\n  industryType: '07'\n}, {\n  name: '装订及其他印刷服务活动',\n  value: '2320',\n  industryType: '07'\n}, {\n  name: '记录媒介的复制',\n  value: '2330',\n  industryType: '07'\n}, {\n  name: '文教、工美、体育和娱乐用品制造业',\n  value: '2400',\n  industryType: '07'\n}, {\n  name: '文教办公用品制造',\n  value: '2410',\n  industryType: '07'\n}, {\n  name: '体育用品制造',\n  value: '2420',\n  industryType: '07'\n}, {\n  name: '乐器制造',\n  value: '2430',\n  industryType: '07'\n}, {\n  name: '玩具制造',\n  value: '2440',\n  industryType: '07'\n}, {\n  name: '游艺器材及娱乐用品制造',\n  value: '2450',\n  industryType: '07'\n}, {\n  name: '工艺美术及礼仪用品制造',\n  value: '2460',\n  industryType: '07'\n}, {\n  name: '石油、煤炭及其他燃料加工业',\n  value: '2500',\n  industryType: '07'\n}, {\n  name: '精炼石油产品制造',\n  value: '2510',\n  industryType: '07'\n}, {\n  name: '核燃料加工',\n  value: '2530',\n  industryType: '07'\n}, {\n  name: '煤炭加工',\n  value: '2540',\n  industryType: '07'\n}, {\n  name: '煤化工',\n  value: '2541',\n  industryType: '07'\n}, {\n  name: '煤制品制造',\n  value: '2542',\n  industryType: '07'\n}, {\n  name: '生物质燃料加工',\n  value: '2550',\n  industryType: '07'\n}, {\n  name: '化学原料和化学制品制造业',\n  value: '2600',\n  industryType: '07'\n}, {\n  name: '基础化学原料制造',\n  value: '2610',\n  industryType: '07'\n}, {\n  name: '肥料制造',\n  value: '2620',\n  industryType: '07'\n}, {\n  name: '氮肥制造',\n  value: '2621',\n  industryType: '07'\n}, {\n  name: '磷肥制造',\n  value: '2622',\n  industryType: '07'\n}, {\n  name: '钾肥制造',\n  value: '2623',\n  industryType: '07'\n}, {\n  name: '复混肥料制造',\n  value: '2624',\n  industryType: '07'\n}, {\n  name: '有机肥料及微生物肥料制造',\n  value: '2625',\n  industryType: '07'\n}, {\n  name: '其他肥料制造',\n  value: '2629',\n  industryType: '07'\n}, {\n  name: '农药制造',\n  value: '2630',\n  industryType: '07'\n}, {\n  name: '化学农药制造',\n  value: '2631',\n  industryType: '07'\n}, {\n  name: '生物化学农药及微生物农药制造',\n  value: '2632',\n  industryType: '07'\n}, {\n  name: '涂料、油墨、颜料及类似产品制造',\n  value: '2640',\n  industryType: '07'\n}, {\n  name: '合成材料制造',\n  value: '2650',\n  industryType: '07'\n}, {\n  name: '专用化学产品制造',\n  value: '2660',\n  industryType: '07'\n}, {\n  name: '日用化学产品制造',\n  value: '2670',\n  industryType: '07'\n}, {\n  name: '氯碱',\n  value: '2690',\n  industryType: '07'\n}, {\n  name: '电石',\n  value: '26A0',\n  industryType: '07'\n}, {\n  name: '黄磷',\n  value: '26B0',\n  industryType: '07'\n}, {\n  name: '炸药、火工及焰火产品制造',\n  value: '26D0',\n  industryType: '07'\n}, {\n  name: '医药制造业',\n  value: '2700',\n  industryType: '07'\n}, {\n  name: '化学药品原料药制造',\n  value: '2710',\n  industryType: '07'\n}, {\n  name: '化学药品制剂制造',\n  value: '2720',\n  industryType: '07'\n}, {\n  name: '中药饮片加工',\n  value: '2730',\n  industryType: '07'\n}, {\n  name: '中成药生产',\n  value: '2740',\n  industryType: '07'\n}, {\n  name: '兽用药品制造',\n  value: '2750',\n  industryType: '07'\n}, {\n  name: '生物药品制品制造',\n  value: '2760',\n  industryType: '07'\n}, {\n  name: '卫生材料及医药用品制造',\n  value: '2770',\n  industryType: '07'\n}, {\n  name: '药用辅料及包装材料',\n  value: '2780',\n  industryType: '07'\n}, {\n  name: '化学纤维制造业',\n  value: '2800',\n  industryType: '07'\n}, {\n  name: '纤维素纤维原料及纤维制造',\n  value: '2810',\n  industryType: '07'\n}, {\n  name: '合成纤维制造',\n  value: '2820',\n  industryType: '07'\n}, {\n  name: '生物基材料制造',\n  value: '2830',\n  industryType: '07'\n}, {\n  name: '橡胶制品业',\n  value: '2900',\n  industryType: '07'\n}, {\n  name: '轮胎制造',\n  value: '2910',\n  industryType: '07'\n}, {\n  name: '橡胶板、管、带的制造',\n  value: '2920',\n  industryType: '07'\n}, {\n  name: '橡胶零件制造',\n  value: '2930',\n  industryType: '07'\n}, {\n  name: '再生橡胶制造',\n  value: '2940',\n  industryType: '07'\n}, {\n  name: '日用及医用橡胶制品制造',\n  value: '2950',\n  industryType: '07'\n}, {\n  name: '运动场地用塑胶制造',\n  value: '2970',\n  industryType: '07'\n}, {\n  name: '其他橡胶制品制造',\n  value: '2990',\n  industryType: '07'\n}, {\n  name: '塑料制品业',\n  value: '3000',\n  industryType: '07'\n}, {\n  name: '塑料薄膜制造',\n  value: '3010',\n  industryType: '07'\n}, {\n  name: '塑料板、管、型材制造',\n  value: '3020',\n  industryType: '07'\n}, {\n  name: '塑料丝、绳及编织品制造',\n  value: '3030',\n  industryType: '07'\n}, {\n  name: '泡沫塑料制造',\n  value: '3040',\n  industryType: '07'\n}, {\n  name: '塑料人造革、合成革制造',\n  value: '3050',\n  industryType: '07'\n}, {\n  name: '塑料包装箱及容器制造',\n  value: '3060',\n  industryType: '07'\n}, {\n  name: '日用塑料制造',\n  value: '3080',\n  industryType: '07'\n}, {\n  name: '塑料零件制造及其他塑料制品制造',\n  value: '3090',\n  industryType: '07'\n}, {\n  name: '人造草坪制造',\n  value: '30B0',\n  industryType: '07'\n}, {\n  name: '非金属矿物制品业',\n  value: '3100',\n  industryType: '07'\n}, {\n  name: '水泥、石灰和石膏制造',\n  value: '3110',\n  industryType: '07'\n}, {\n  name: '水泥制造',\n  value: '3111',\n  industryType: '07'\n}, {\n  name: '石灰和石膏制造',\n  value: '3112',\n  industryType: '07'\n}, {\n  name: '石膏、水泥制品及类似制品制造',\n  value: '3120',\n  industryType: '07'\n}, {\n  name: '砖瓦、石材等建筑材料制造',\n  value: '3130',\n  industryType: '07'\n}, {\n  name: '玻璃制造',\n  value: '3140',\n  industryType: '07'\n}, {\n  name: '平板玻璃制造',\n  value: '3141',\n  industryType: '07'\n}, {\n  name: '特种玻璃制造',\n  value: '314A',\n  industryType: '07'\n}, {\n  name: '其他玻璃制造',\n  value: '314B',\n  industryType: '07'\n}, {\n  name: '陶瓷制品制造',\n  value: '3150',\n  industryType: '07'\n}, {\n  name: '卫生陶瓷制品制造',\n  value: '3151',\n  industryType: '07'\n}, {\n  name: '特种陶瓷制品制造',\n  value: '3152',\n  industryType: '07'\n}, {\n  name: '日用陶瓷制品制造',\n  value: '3153',\n  industryType: '07'\n}, {\n  name: '建筑陶瓷制品制造',\n  value: '3154',\n  industryType: '07'\n}, {\n  name: '园艺陶瓷制造',\n  value: '3155',\n  industryType: '07'\n}, {\n  name: '陈设艺术陶瓷制造',\n  value: '3156',\n  industryType: '07'\n}, {\n  name: '其他陶瓷制品制造',\n  value: '3159',\n  industryType: '07'\n}, {\n  name: '耐火材料制品制造',\n  value: '3160',\n  industryType: '07'\n}, {\n  name: '玻璃制品制造',\n  value: '3170',\n  industryType: '07'\n}, {\n  name: '技术玻璃制品制造',\n  value: '3171',\n  industryType: '07'\n}, {\n  name: '光学玻璃制造',\n  value: '3172',\n  industryType: '07'\n}, {\n  name: '玻璃仪器制造',\n  value: '3173',\n  industryType: '07'\n}, {\n  name: '日用玻璃制品制造',\n  value: '3174',\n  industryType: '07'\n}, {\n  name: '玻璃包装容器制造',\n  value: '3175',\n  industryType: '07'\n}, {\n  name: '玻璃保温容器制造',\n  value: '3176',\n  industryType: '07'\n}, {\n  name: '制镜及类似品加工',\n  value: '3177',\n  industryType: '07'\n}, {\n  name: '其他玻璃制品制造',\n  value: '3179',\n  industryType: '07'\n}, {\n  name: '玻璃纤维和玻璃纤维增强塑料制品制造',\n  value: '3180',\n  industryType: '07'\n}, {\n  name: '玻璃纤维及制品制造',\n  value: '3181',\n  industryType: '07'\n}, {\n  name: '玻璃纤维增强塑料制品制造',\n  value: '3182',\n  industryType: '07'\n}, {\n  name: '石墨及其他非金属矿物制品制造',\n  value: '3190',\n  industryType: '07'\n}, {\n  name: '碳化硅',\n  value: '31C0',\n  industryType: '07'\n}, {\n  name: '黑色金属冶炼和压延加工业',\n  value: '3200',\n  industryType: '07'\n}, {\n  name: '炼铁',\n  value: '3210',\n  industryType: '07'\n}, {\n  name: '炼钢',\n  value: '3220',\n  industryType: '07'\n}, {\n  name: '钢压延加工',\n  value: '3230',\n  industryType: '07'\n}, {\n  name: '铁合金冶炼',\n  value: '3240',\n  industryType: '07'\n}, {\n  name: '有色金属冶炼和压延加工业',\n  value: '3300',\n  industryType: '07'\n}, {\n  name: '常用有色金属冶炼',\n  value: '3310',\n  industryType: '07'\n}, {\n  name: '铝冶炼',\n  value: '3316',\n  industryType: '07'\n}, {\n  name: '铅锌冶炼',\n  value: '3317',\n  industryType: '07'\n}, {\n  name: '其他常用有色金属冶炼',\n  value: '3319',\n  industryType: '07'\n}, {\n  name: '贵金属冶炼',\n  value: '3320',\n  industryType: '07'\n}, {\n  name: '稀有稀土金属冶炼',\n  value: '3330',\n  industryType: '07'\n}, {\n  name: '有色金属合金制造',\n  value: '3340',\n  industryType: '07'\n}, {\n  name: '有色金属压延加工',\n  value: '3350',\n  industryType: '07'\n}, {\n  name: '金属制品业',\n  value: '3400',\n  industryType: '07'\n}, {\n  name: '结构性金属制品制造',\n  value: '3410',\n  industryType: '07'\n}, {\n  name: '金属工具制造',\n  value: '3420',\n  industryType: '07'\n}, {\n  name: '切削工具制造',\n  value: '3421',\n  industryType: '07'\n}, {\n  name: '手工具制造',\n  value: '3422',\n  industryType: '07'\n}, {\n  name: '农用及园林用金属工具制造',\n  value: '3423',\n  industryType: '07'\n}, {\n  name: '刀剪及类似日用金属工具制造',\n  value: '3424',\n  industryType: '07'\n}, {\n  name: '其他金属工具制造',\n  value: '3429',\n  industryType: '07'\n}, {\n  name: '集装箱及金属包装容器制造',\n  value: '3430',\n  industryType: '07'\n}, {\n  name: '金属丝绳及其制品制造',\n  value: '3440',\n  industryType: '07'\n}, {\n  name: '建筑、安全用金属制品制造',\n  value: '3450',\n  industryType: '07'\n}, {\n  name: '金属表面处理及热处理加工',\n  value: '3460',\n  industryType: '07'\n}, {\n  name: '搪瓷制品制造',\n  value: '3470',\n  industryType: '07'\n}, {\n  name: '生产专用搪瓷制品制造',\n  value: '3471',\n  industryType: '07'\n}, {\n  name: '搪瓷卫生洁具制造',\n  value: '3472',\n  industryType: '07'\n}, {\n  name: '建筑装饰搪瓷制品制造',\n  value: '3473',\n  industryType: '07'\n}, {\n  name: '搪瓷日用品及其他搪瓷制品制造',\n  value: '3479',\n  industryType: '07'\n}, {\n  name: '金属制日用品制造',\n  value: '3480',\n  industryType: '07'\n}, {\n  name: '铸造及其他金属制品制造',\n  value: '3490',\n  industryType: '07'\n}, {\n  name: '通用设备制造业',\n  value: '3500',\n  industryType: '07'\n}, {\n  name: '锅炉及原动设备制造',\n  value: '3510',\n  industryType: '07'\n}, {\n  name: '风能原动设备制造',\n  value: '3511',\n  industryType: '07'\n}, {\n  name: '锅炉及辅助设备、其他原动设备制造',\n  value: '3512',\n  industryType: '07'\n}, {\n  name: '金属加工机械制造',\n  value: '3520',\n  industryType: '07'\n}, {\n  name: '物料搬运设备制造',\n  value: '3530',\n  industryType: '07'\n}, {\n  name: '泵、阀门、压缩机及类似机械制造',\n  value: '3540',\n  industryType: '07'\n}, {\n  name: '轴承、齿轮和传动部件制造',\n  value: '3550',\n  industryType: '07'\n}, {\n  name: '烘炉、风机、包装等设备制造',\n  value: '3560',\n  industryType: '07'\n}, {\n  name: '通用零部件制造',\n  value: '3580',\n  industryType: '07'\n}, {\n  name: '文化、办公用机械制造',\n  value: '35A0',\n  industryType: '07'\n}, {\n  name: '其他通用设备制造业',\n  value: '35B0',\n  industryType: '07'\n}, {\n  name: '工业机器人制造',\n  value: '35B1',\n  industryType: '07'\n}, {\n  name: '特殊作业机器人制造',\n  value: '35B2',\n  industryType: '07'\n}, {\n  name: '增材制造装备制造',\n  value: '35B3',\n  industryType: '07'\n}, {\n  name: '其他未列明通用设备制造业',\n  value: '35B9',\n  industryType: '07'\n}, {\n  name: '专用设备制造业',\n  value: '3600',\n  industryType: '07'\n}, {\n  name: '采矿、冶金、建筑专用设备制造',\n  value: '3610',\n  industryType: '07'\n}, {\n  name: '化工、木材、非金属加工专用设备制造',\n  value: '3620',\n  industryType: '07'\n}, {\n  name: '食品、饮料、烟草及饲料生产专用设备制造',\n  value: '3630',\n  industryType: '07'\n}, {\n  name: '印刷、制药、日化及日用品生产专用设备制造',\n  value: '3640',\n  industryType: '07'\n}, {\n  name: '纺织、服装和皮革工业专用设备制造',\n  value: '3650',\n  industryType: '07'\n}, {\n  name: '电子和电工机械专用设备制造',\n  value: '3660',\n  industryType: '07'\n}, {\n  name: '农、林、牧、渔专用机械制造',\n  value: '3670',\n  industryType: '07'\n}, {\n  name: '医疗仪器设备及器械制造',\n  value: '3680',\n  industryType: '07'\n}, {\n  name: '环保、邮政、社会公共服务及其他专用设备制造',\n  value: '3690',\n  industryType: '07'\n}, {\n  name: '铁路、船舶、航空航天和其他运输设备制造业',\n  value: '3700',\n  industryType: '07'\n}, {\n  name: '铁路运输设备制造',\n  value: '3710',\n  industryType: '07'\n}, {\n  name: '摩托车制造',\n  value: '3730',\n  industryType: '07'\n}, {\n  name: '自行车和残疾人座车制造',\n  value: '3740',\n  industryType: '07'\n}, {\n  name: '船舶及相关装置制造',\n  value: '3750',\n  industryType: '07'\n}, {\n  name: '航空、航天器及设备制造',\n  value: '3760',\n  industryType: '07'\n}, {\n  name: '城市轨道交通设备制造',\n  value: '3770',\n  industryType: '07'\n}, {\n  name: '助动车制造',\n  value: '3780',\n  industryType: '07'\n}, {\n  name: '交通器材及其他交通运输设备制造',\n  value: '3790',\n  industryType: '07'\n}, {\n  name: '非公路休闲车及零配件制造',\n  value: '37A0',\n  industryType: '07'\n}, {\n  name: '潜水救捞及其他未列明运输设备制造',\n  value: '37B0',\n  industryType: '07'\n}, {\n  name: '汽车制造业',\n  value: '3800',\n  industryType: '07'\n}, {\n  name: '汽车整车制造',\n  value: '3810',\n  industryType: '07'\n}, {\n  name: '汽柴油车整车制造',\n  value: '3811',\n  industryType: '07'\n}, {\n  name: '新能源车整车制造',\n  value: '3812',\n  industryType: '07'\n}, {\n  name: '汽车用发动机制造',\n  value: '3820',\n  industryType: '07'\n}, {\n  name: '改装汽车制造',\n  value: '3830',\n  industryType: '07'\n}, {\n  name: '低速汽车制造',\n  value: '3840',\n  industryType: '07'\n}, {\n  name: '电车制造',\n  value: '3850',\n  industryType: '07'\n}, {\n  name: '汽车车身、挂车制造',\n  value: '3860',\n  industryType: '07'\n}, {\n  name: '汽车零部件及配件制造',\n  value: '3870',\n  industryType: '07'\n}, {\n  name: '电气机械和器材制造业',\n  value: '3900',\n  industryType: '07'\n}, {\n  name: '电机制造',\n  value: '3910',\n  industryType: '07'\n}, {\n  name: '输配电及控制设备制造',\n  value: '3920',\n  industryType: '07'\n}, {\n  name: '光伏设备及元器件制造',\n  value: '3921',\n  industryType: '07'\n}, {\n  name: '其他输配电及控制设备制造',\n  value: '3929',\n  industryType: '07'\n}, {\n  name: '电线、电缆、光缆及电工器材制造',\n  value: '3930',\n  industryType: '07'\n}, {\n  name: '电池制造',\n  value: '3940',\n  industryType: '07'\n}, {\n  name: '家用电力器具制造',\n  value: '3950',\n  industryType: '07'\n}, {\n  name: '非电力家用器具制造',\n  value: '3960',\n  industryType: '07'\n}, {\n  name: '照明器具制造',\n  value: '3970',\n  industryType: '07'\n}, {\n  name: '其他电气机械及器材制造',\n  value: '3990',\n  industryType: '07'\n}, {\n  name: '计算机、通信和其他电子设备制造业',\n  value: '4000',\n  industryType: '07'\n}, {\n  name: '通信设备制造',\n  value: '4010',\n  industryType: '07'\n}, {\n  name: '雷达及配套设备制造',\n  value: '4020',\n  industryType: '07'\n}, {\n  name: '广播电视设备制造',\n  value: '4030',\n  industryType: '07'\n}, {\n  name: '计算机制造',\n  value: '4040',\n  industryType: '07'\n}, {\n  name: '电子器件制造',\n  value: '4050',\n  industryType: '07'\n}, {\n  name: '电子元件及电子专用材料制造',\n  value: '4060',\n  industryType: '07'\n}, {\n  name: '非专业视听设备制造',\n  value: '4070',\n  industryType: '07'\n}, {\n  name: '智能消费设备制造',\n  value: '4080',\n  industryType: '07'\n}, {\n  name: '服务消费机器人制造',\n  value: '4081',\n  industryType: '07'\n}, {\n  name: '智能无人飞行器制造',\n  value: '4082',\n  industryType: '07'\n}, {\n  name: '可穿戴智能设备制造',\n  value: '4083',\n  industryType: '07'\n}, {\n  name: '智能车载设备制造',\n  value: '4084',\n  industryType: '07'\n}, {\n  name: '其他智能消费设备制造',\n  value: '4085',\n  industryType: '07'\n}, {\n  name: '其他电子设备制造',\n  value: '4090',\n  industryType: '07'\n}, {\n  name: '仪器仪表制造业',\n  value: '4100',\n  industryType: '07'\n}, {\n  name: '通用仪器仪表制造',\n  value: '4110',\n  industryType: '07'\n}, {\n  name: '专用仪器仪表制造',\n  value: '4120',\n  industryType: '07'\n}, {\n  name: '钟表与计时仪器制造',\n  value: '4130',\n  industryType: '07'\n}, {\n  name: '光学仪器制造',\n  value: '4140',\n  industryType: '07'\n}, {\n  name: '光学仪器制造',\n  value: '4141',\n  industryType: '07'\n}, {\n  name: '衡器制造',\n  value: '4160',\n  industryType: '07'\n}, {\n  name: '其他仪器仪表的制造',\n  value: '4190',\n  industryType: '07'\n}, {\n  name: '其他制造业',\n  value: '4200',\n  industryType: '07'\n}, {\n  name: '日用杂品制造',\n  value: '4220',\n  industryType: '07'\n}, {\n  name: '核辐射加工',\n  value: '4240',\n  industryType: '07'\n}, {\n  name: '其他未列明的制造业',\n  value: '4290',\n  industryType: '07'\n}, {\n  name: '废弃资源综合利用业',\n  value: '4300',\n  industryType: '07'\n}, {\n  name: '金属废料和碎屑加工处理',\n  value: '4310',\n  industryType: '07'\n}, {\n  name: '非金属废料和碎屑加工处理',\n  value: '4320',\n  industryType: '07'\n}, {\n  name: '电力、热力生产和供应业',\n  value: '4400',\n  industryType: '07'\n}, {\n  name: '其中:电厂生产全部耗用电量',\n  value: '4410',\n  industryType: '07'\n}, {\n  name: '线路损失电量',\n  value: '4420',\n  industryType: '07'\n}, {\n  name: '抽水蓄能耗用电量',\n  value: '4430',\n  industryType: '07'\n}, {\n  name: '电力生产',\n  value: '4440',\n  industryType: '07'\n}, {\n  name: '火力发电',\n  value: '4441',\n  industryType: '07'\n}, {\n  name: '水力发电',\n  value: '4442',\n  industryType: '07'\n}, {\n  name: '核力发电',\n  value: '4443',\n  industryType: '07'\n}, {\n  name: '风力发电',\n  value: '4444',\n  industryType: '07'\n}, {\n  name: '太阳能发电',\n  value: '4445',\n  industryType: '07'\n}, {\n  name: '其他电力生产',\n  value: '4446',\n  industryType: '07'\n}, {\n  name: '生物质能发电',\n  value: '4447',\n  industryType: '07'\n}, {\n  name: '热电联产',\n  value: '4448',\n  industryType: '07'\n}, {\n  name: '电力供应',\n  value: '4450',\n  industryType: '07'\n}, {\n  name: '热力生产和供应',\n  value: '4460',\n  industryType: '07'\n}, {\n  name: '燃气生产和供应业',\n  value: '4500',\n  industryType: '07'\n}, {\n  name: '水的生产和供应业',\n  value: '4600',\n  industryType: '07'\n}, {\n  name: '自来水生产和供应',\n  value: '4610',\n  industryType: '07'\n}, {\n  name: '污水处理及其再生利用',\n  value: '4620',\n  industryType: '07'\n}, {\n  name: '海水淡化处理',\n  value: '4630',\n  industryType: '07'\n}, {\n  name: '其他水的处理、利用与分配',\n  value: '4690',\n  industryType: '07'\n}, {\n  name: '金属制品、机械和设备修理业',\n  value: '4B00',\n  industryType: '07'\n}, {\n  name: '金属制品修理',\n  value: '4B10',\n  industryType: '07'\n}, {\n  name: '通用设备修理',\n  value: '4B20',\n  industryType: '07'\n}, {\n  name: '专用设备修理',\n  value: '4B30',\n  industryType: '07'\n}, {\n  name: '铁路、船舶、航空航天等运输设备修理',\n  value: '4B40',\n  industryType: '07'\n}, {\n  name: '电气设备修理',\n  value: '4B50',\n  industryType: '07'\n}, {\n  name: '仪器仪表修理',\n  value: '4B60',\n  industryType: '07'\n}, {\n  name: '其他机械和设备修理业',\n  value: '4B90',\n  industryType: '07'\n}];\n\n// 评估类别\nvar assessMode = [{\n  name: '企业情况自评估',\n  value: '01'\n}, {\n  name: '典型企业负荷特性估算',\n  value: '02'\n}];\n\n// 用电分类\nvar electrClassify = [\n// 12月份\n{\n  name: '一般工商业，单一制，不满1千伏',\n  value: '01',\n  content: '0.7920'\n}, {\n  name: '一般工商业，单一制，10千伏',\n  value: '02',\n  content: '0.7469'\n}, {\n  name: '一般工商业，单一制，35千伏',\n  value: '03',\n  content: '0.7023'\n}, {\n  name: '一般工商业，两部制，不满1千伏',\n  value: '04',\n  content: '0.6620'\n}, {\n  name: '一般工商业，两部制，10千伏',\n  value: '05',\n  content: '0.6436'\n}, {\n  name: '一般工商业，两部制，35千伏',\n  value: '06',\n  content: '0.6120'\n}, {\n  name: '一般工商业，两部制，110千伏',\n  value: '07',\n  content: '0.5816'\n}, {\n  name: '一般工商业，两部制，220千伏及以上',\n  value: '08',\n  content: '0.5715'\n}, {\n  name: '大工业，两部制，不满1千伏',\n  value: '09',\n  content: '0.7398'\n}, {\n  name: '大工业，两部制，10千伏',\n  value: '10',\n  content: '0.7203'\n}, {\n  name: '大工业，两部制，35千伏',\n  value: '11',\n  content: '0.6711'\n}, {\n  name: '大工业，两部制，110千伏',\n  value: '12',\n  content: '0.6415'\n}, {\n  name: '大工业，两部制，220千伏及以上',\n  value: '13',\n  content: '0.6291'\n}\n\n// // 11月份\n// { name: '一般工商业，单一制，不满1千伏', value: '01', content: '0.7852' },\n// { name: '一般工商业，单一制，10千伏', value: '02', content: '0.7401' },\n// { name: '一般工商业，单一制，35千伏', value: '03', content: '0.6955' },\n// { name: '一般工商业，两部制，不满1千伏', value: '04', content: '0.6552' },\n// { name: '一般工商业，两部制，10千伏', value: '05', content: '0.6368' },\n// { name: '一般工商业，两部制，35千伏', value: '06', content: '0.6052' },\n// { name: '一般工商业，两部制，110千伏', value: '07', content: '0.5748' },\n// { name: '一般工商业，两部制，220千伏及以上', value: '08', content: '0.5647' },\n// { name: '大工业，两部制，不满1千伏', value: '09', content: '0.7330' },\n// { name: '大工业，两部制，10千伏', value: '10', content: '0.7135' },\n// { name: '大工业，两部制，35千伏', value: '11', content: '0.6643' },\n// { name: '大工业，两部制，110千伏', value: '12', content: '0.6347' },\n// { name: '大工业，两部制，220千伏及以上', value: '13', content: '0.6223' }\n\n// // 10月份\n// { name: '一般工商业，单一制，不满1千伏', value: '01', content: '0.7819' },\n// { name: '一般工商业，单一制，10千伏', value: '02', content: '0.7368' },\n// { name: '一般工商业，单一制，35千伏', value: '03', content: '0.6922' },\n// { name: '一般工商业，两部制，不满1千伏', value: '04', content: '0.6519' },\n// { name: '一般工商业，两部制，10千伏', value: '05', content: '0.6335' },\n// { name: '一般工商业，两部制，35千伏', value: '06', content: '0.6019' },\n// { name: '一般工商业，两部制，110千伏', value: '07', content: '0.5715' },\n// { name: '一般工商业，两部制，220千伏及以上', value: '08', content: '0.5614' },\n// { name: '大工业，两部制，不满1千伏', value: '09', content: '0.7297' },\n// { name: '大工业，两部制，10千伏', value: '10', content: '0.7102' },\n// { name: '大工业，两部制，35千伏', value: '11', content: '0.6610' },\n// { name: '大工业，两部制，110千伏', value: '12', content: '0.6314' },\n// { name: '大工业，两部制，220千伏及以上', value: '13', content: '0.6190' }\n\n// // 9月份\n// { name: '一般工商业，单一制，不满1千伏', value: '01', content: '0.8350' },\n// { name: '一般工商业，单一制，10千伏', value: '02', content: '0.7899' },\n// { name: '一般工商业，单一制，35千伏', value: '03', content: '0.7453' },\n// { name: '一般工商业，两部制，不满1千伏', value: '04', content: '0.7050' },\n// { name: '一般工商业，两部制，10千伏', value: '05', content: '0.6866' },\n// { name: '一般工商业，两部制，35千伏', value: '06', content: '0.6550' },\n// { name: '一般工商业，两部制，110千伏', value: '07', content: '0.6246' },\n// { name: '一般工商业，两部制，220千伏及以上', value: '08', content: '0.6145' },\n// { name: '大工业，两部制，不满1千伏', value: '09', content: '0.7828' },\n// { name: '大工业，两部制，10千伏', value: '10', content: '0.7633' },\n// { name: '大工业，两部制，35千伏', value: '11', content: '0.7141' },\n// { name: '大工业，两部制，110千伏', value: '12', content: '0.6845' },\n// { name: '大工业，两部制，220千伏及以上', value: '13', content: '0.6721' }\n\n// // 8月份\n// { name: '一般工商业，单一制，不满1千伏', value: '01', content: '0.8378' },\n// { name: '一般工商业，单一制，10千伏', value: '02', content: '0.7927' },\n// { name: '一般工商业，单一制，35千伏', value: '03', content: '0.7481' },\n// { name: '一般工商业，两部制，不满1千伏', value: '04', content: '0.7078' },\n// { name: '一般工商业，两部制，10千伏', value: '05', content: '0.6894' },\n// { name: '一般工商业，两部制，35千伏', value: '06', content: '0.6578' },\n// { name: '一般工商业，两部制，110千伏', value: '07', content: '0.6274' },\n// { name: '一般工商业，两部制，220千伏及以上', value: '08', content: '0.6173' },\n// { name: '大工业，两部制，不满1千伏', value: '09', content: '0.7856' },\n// { name: '大工业，两部制，10千伏', value: '10', content: '0.7661' },\n// { name: '大工业，两部制，35千伏', value: '11', content: '0.7169' },\n// { name: '大工业，两部制，110千伏', value: '12', content: '0.6873' },\n// { name: '大工业，两部制，220千伏及以上', value: '13', content: '0.6749' }\n];\n// 调节深度\nvar depthAdjustment = [{\n  name: '一级(温度调整)',\n  value: '01',\n  content: '15%'\n}, {\n  name: '二级(部分停用)',\n  value: '02',\n  content: '30%'\n}, {\n  name: '三级(较多停用)',\n  value: '03',\n  content: '46%'\n}];\n// 时段\nvar timeFrame = [{\n  name: '尖峰时段',\n  value: '36'\n}, {\n  name: '峰时段',\n  value: '37'\n}, {\n  name: '谷时段',\n  value: '38'\n}, {\n  name: '平时段',\n  value: '39'\n}];\n// 电压分类页面使用\nvar voltageClasses = [{\n  name: '35千伏及以下',\n  value: '01',\n  content: '40.8'\n}, {\n  name: '110千伏及以上',\n  value: '02',\n  content: '38.4'\n}];\n// 电压等级标码\nvar baseVolt = [{\n  codeType: 'baseVolt',\n  codeValue: '0101',\n  codeName: '交流6V',\n  content: '6',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0102',\n  codeName: '交流12V',\n  content: '12',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0103',\n  codeName: '交流24V',\n  content: '24',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0104',\n  codeName: '交流36V',\n  content: '36',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0105',\n  codeName: '交流48V',\n  content: '48',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0106',\n  codeName: '交流110V',\n  content: '110',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0107',\n  codeName: '交流220V',\n  content: '220',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0108',\n  codeName: '交流380V',\n  content: '380',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0109',\n  codeName: '交流600V',\n  content: '600',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0110',\n  codeName: '交流660V',\n  content: '660',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0111',\n  codeName: '交流750V',\n  content: '750',\n  content1: '不满1千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0112',\n  codeName: '交流1000V（含1140V）',\n  content: '1000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0113',\n  codeName: '交流1500V',\n  content: '1500'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0114',\n  codeName: '交流2500V',\n  content: '2500'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0115',\n  codeName: '交流3000V',\n  content: '3000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0116',\n  codeName: '交流6kV',\n  content: '6000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0117',\n  codeName: '交流10kV',\n  content: '10000',\n  content1: '10千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0118',\n  codeName: '交流13.8kV',\n  content: '13800'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0119',\n  codeName: '交流20kV',\n  content: '20000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0120',\n  codeName: '交流27.5kV',\n  content: '27500'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0121',\n  codeName: '交流35kV',\n  content: '35000',\n  content1: '35千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0142',\n  codeName: '交流55kV',\n  content: '55000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0122',\n  codeName: '交流66kV',\n  content: '66000'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0123',\n  codeName: '交流110kV',\n  content: '110000',\n  content1: '110千伏'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0124',\n  codeName: '交流220kV',\n  content: '220000',\n  content1: '220千伏及以上'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0125',\n  codeName: '交流330kV',\n  content: '330000',\n  content1: '220千伏及以上'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0126',\n  codeName: '交流500kV',\n  content: '500000',\n  content1: '220千伏及以上'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0127',\n  codeName: '交流750kV',\n  content: '750000',\n  content1: '220千伏及以上'\n}, {\n  codeType: 'baseVolt',\n  codeValue: '0128',\n  codeName: '交流1000kV',\n  content: '1000000',\n  content1: '220千伏及以上'\n}];\n// 通知时间 price价格； timeFrame时段； maxResponseTime最大可响应时长\nvar noticeTime = [{\n  name: '不参与',\n  value: '01',\n  price: '0',\n  timeFrame: '0',\n  maxResponseTime: '0'\n}, {\n  name: '>24小时',\n  value: '02',\n  price: '2.4',\n  timeFrame: '12:00-16:00',\n  maxResponseTime: '4'\n}, {\n  name: '(8,24]小时',\n  value: '03',\n  price: '2.7',\n  timeFrame: '12:00-16:00',\n  maxResponseTime: '4'\n}, {\n  name: '(2,8]小时',\n  value: '04',\n  price: '3',\n  timeFrame: '12:00-16:00',\n  maxResponseTime: '4'\n}, {\n  name: '(0.5,2]小时',\n  value: '05',\n  price: '4.5',\n  timeFrame: '12:30-13:30',\n  maxResponseTime: '1'\n}, {\n  name: '(0,0.5]小时',\n  value: '06',\n  price: '6',\n  timeFrame: '12:30-13:30',\n  maxResponseTime: '1'\n}, {\n  name: '无需提前通知，即时响应',\n  value: '07',\n  price: '9',\n  timeFrame: '12:30-13:30',\n  maxResponseTime: '1'\n}];\nvar electricityClassificationList = [{\n  name: '工商业及其他用电',\n  value: '01'\n}, {\n  name: '一般工商业及其他用电',\n  value: '02'\n}, {\n  name: '大工业用电',\n  value: '03'\n}, {\n  name: '工商业及其他用电（315千伏安以下）',\n  value: '04'\n}, {\n  name: '工商业及其他用电（315千伏安及以上）',\n  value: '05'\n}];\nvar electricityPriceStrategyList = [{\n  name: '单一制',\n  value: '01'\n}, {\n  name: '两部制',\n  value: '02'\n}];\nvar voltageGradeList = [{\n  name: '不满1千伏',\n  value: '01'\n}, {\n  name: '1-10千伏',\n  value: '02'\n}, {\n  name: '35千伏',\n  value: '03'\n}, {\n  name: '110千伏',\n  value: '04'\n}, {\n  name: '220千伏',\n  value: '05'\n}, {\n  name: '1-10（20）千伏',\n  value: '06'\n}, {\n  name: '220（330）千伏',\n  value: '07'\n}, {\n  name: '330千伏及以上',\n  value: '08'\n}, {\n  name: '20千伏',\n  value: '09'\n}, {\n  name: '110 （66 千伏）',\n  value: '10'\n}, {\n  name: '220千伏及以上',\n  value: '11'\n}, {\n  name: '20-35千伏以下',\n  value: '12'\n}];\n\n//# sourceURL=webpack:///./src/const.js?");

/***/ })

}]);