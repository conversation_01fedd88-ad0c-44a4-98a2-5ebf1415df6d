(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[3],{

/***/ "./node_modules/big.js/big.js":
/*!************************************!*\
  !*** ./node_modules/big.js/big.js ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 <PERSON>\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\n;\n(function (GLOBAL) {\n  'use strict';\n\n  var Big,\n    /************************************** EDITABLE DEFAULTS *****************************************/\n\n    // The default values below must be integers within the stated ranges.\n\n    /*\r\n     * The maximum number of decimal places (DP) of the results of operations involving division:\r\n     * div and sqrt, and pow with negative exponents.\r\n     */\n    DP = 20,\n    // 0 to MAX_DP\n\n    /*\r\n     * The rounding mode (RM) used when rounding to the above decimal places.\r\n     *\r\n     *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n     *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n     *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n     *  3  Away from zero.                                  (ROUND_UP)\r\n     */\n    RM = 1,\n    // 0, 1, 2 or 3\n\n    // The maximum value of DP and Big.DP.\n    MAX_DP = 1E6,\n    // 0 to 1000000\n\n    // The maximum magnitude of the exponent argument to the pow method.\n    MAX_POWER = 1E6,\n    // 1 to 1000000\n\n    /*\r\n     * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n     * (JavaScript numbers: -7)\r\n     * -1000000 is the minimum recommended exponent value of a Big.\r\n     */\n    NE = -7,\n    // 0 to -1000000\n\n    /*\r\n     * The positive exponent (PE) at and above which toString returns exponential notation.\r\n     * (JavaScript numbers: 21)\r\n     * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n     */\n    PE = 21,\n    // 0 to 1000000\n\n    /*\r\n     * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n     * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n     * primitive number without a loss of precision.\r\n     */\n    STRICT = false,\n    // true or false\n\n    /**************************************************************************************************/\n\n    // Error messages.\n    NAME = '[big.js] ',\n    INVALID = NAME + 'Invalid ',\n    INVALID_DP = INVALID + 'decimal places',\n    INVALID_RM = INVALID + 'rounding mode',\n    DIV_BY_ZERO = NAME + 'Division by zero',\n    // The shared prototype object.\n    P = {},\n    UNDEFINED = void 0,\n    NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\n\n  /*\r\n   * Create and return a Big constructor.\r\n   */\n  function _Big_() {\n    /*\r\n     * The Big constructor and exported function.\r\n     * Create and return a new instance of a Big number object.\r\n     *\r\n     * n {number|string|Big} A numeric value.\r\n     */\n    function Big(n) {\n      var x = this;\n\n      // Enable constructor usage without new.\n      if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\n\n      // Duplicate.\n      if (n instanceof Big) {\n        x.s = n.s;\n        x.e = n.e;\n        x.c = n.c.slice();\n      } else {\n        if (typeof n !== 'string') {\n          if (Big.strict === true && typeof n !== 'bigint') {\n            throw TypeError(INVALID + 'value');\n          }\n\n          // Minus zero?\n          n = n === 0 && 1 / n < 0 ? '-0' : String(n);\n        }\n        parse(x, n);\n      }\n\n      // Retain a reference to this Big constructor.\n      // Shadow Big.prototype.constructor which points to Object.\n      x.constructor = Big;\n    }\n    Big.prototype = P;\n    Big.DP = DP;\n    Big.RM = RM;\n    Big.NE = NE;\n    Big.PE = PE;\n    Big.strict = STRICT;\n    Big.roundDown = 0;\n    Big.roundHalfUp = 1;\n    Big.roundHalfEven = 2;\n    Big.roundUp = 3;\n    return Big;\n  }\n\n  /*\r\n   * Parse the number or string value passed to a Big constructor.\r\n   *\r\n   * x {Big} A Big number instance.\r\n   * n {number|string} A numeric value.\r\n   */\n  function parse(x, n) {\n    var e, i, nl;\n    if (!NUMERIC.test(n)) {\n      throw Error(INVALID + 'number');\n    }\n\n    // Determine sign.\n    x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\n\n    // Decimal point?\n    if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\n\n    // Exponential form?\n    if ((i = n.search(/e/i)) > 0) {\n      // Determine exponent.\n      if (e < 0) e = i;\n      e += +n.slice(i + 1);\n      n = n.substring(0, i);\n    } else if (e < 0) {\n      // Integer.\n      e = n.length;\n    }\n    nl = n.length;\n\n    // Determine leading zeros.\n    for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\n    if (i == nl) {\n      // Zero.\n      x.c = [x.e = 0];\n    } else {\n      // Determine trailing zeros.\n      for (; nl > 0 && n.charAt(--nl) == '0';);\n      x.e = e - i - 1;\n      x.c = [];\n\n      // Convert string to array of digits without leading/trailing zeros.\n      for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\n    }\n    return x;\n  }\n\n  /*\r\n   * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n   *\r\n   * x {Big} The Big to round.\r\n   * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n   * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   * [more] {boolean} Whether the result of division was truncated.\r\n   */\n  function round(x, sd, rm, more) {\n    var xc = x.c;\n    if (rm === UNDEFINED) rm = x.constructor.RM;\n    if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\n      throw Error(INVALID_RM);\n    }\n    if (sd < 1) {\n      more = rm === 3 && (more || !!xc[0]) || sd === 0 && (rm === 1 && xc[0] >= 5 || rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED)));\n      xc.length = 1;\n      if (more) {\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n        x.e = x.e - sd + 1;\n        xc[0] = 1;\n      } else {\n        // Zero.\n        xc[0] = x.e = 0;\n      }\n    } else if (sd < xc.length) {\n      // xc[sd] is the digit after the digit that may be rounded up.\n      more = rm === 1 && xc[sd] >= 5 || rm === 2 && (xc[sd] > 5 || xc[sd] === 5 && (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) || rm === 3 && (more || !!xc[0]);\n\n      // Remove any digits after the required precision.\n      xc.length = sd;\n\n      // Round up?\n      if (more) {\n        // Rounding up may mean the previous digit has to be rounded up.\n        for (; ++xc[--sd] > 9;) {\n          xc[sd] = 0;\n          if (sd === 0) {\n            ++x.e;\n            xc.unshift(1);\n            break;\n          }\n        }\n      }\n\n      // Remove trailing zeros.\n      for (sd = xc.length; !xc[--sd];) xc.pop();\n    }\n    return x;\n  }\n\n  /*\r\n   * Return a string representing the value of Big x in normal or exponential notation.\r\n   * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n   */\n  function stringify(x, doExponential, isNonzero) {\n    var e = x.e,\n      s = x.c.join(''),\n      n = s.length;\n\n    // Exponential notation?\n    if (doExponential) {\n      s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\n\n      // Normal notation.\n    } else if (e < 0) {\n      for (; ++e;) s = '0' + s;\n      s = '0.' + s;\n    } else if (e > 0) {\n      if (++e > n) {\n        for (e -= n; e--;) s += '0';\n      } else if (e < n) {\n        s = s.slice(0, e) + '.' + s.slice(e);\n      }\n    } else if (n > 1) {\n      s = s.charAt(0) + '.' + s.slice(1);\n    }\n    return x.s < 0 && isNonzero ? '-' + s : s;\n  }\n\n  // Prototype/instance methods\n\n  /*\r\n   * Return a new Big whose value is the absolute value of this Big.\r\n   */\n  P.abs = function () {\n    var x = new this.constructor(this);\n    x.s = 1;\n    return x;\n  };\n\n  /*\r\n   * Return 1 if the value of this Big is greater than the value of Big y,\r\n   *       -1 if the value of this Big is less than the value of Big y, or\r\n   *        0 if they have the same value.\r\n   */\n  P.cmp = function (y) {\n    var isneg,\n      x = this,\n      xc = x.c,\n      yc = (y = new x.constructor(y)).c,\n      i = x.s,\n      j = y.s,\n      k = x.e,\n      l = y.e;\n\n    // Either zero?\n    if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\n\n    // Signs differ?\n    if (i != j) return i;\n    isneg = i < 0;\n\n    // Compare exponents.\n    if (k != l) return k > l ^ isneg ? 1 : -1;\n    j = (k = xc.length) < (l = yc.length) ? k : l;\n\n    // Compare digit by digit.\n    for (i = -1; ++i < j;) {\n      if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\n    }\n\n    // Compare lengths.\n    return k == l ? 0 : k > l ^ isneg ? 1 : -1;\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n   * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n   */\n  P.div = function (y) {\n    var x = this,\n      Big = x.constructor,\n      a = x.c,\n      // dividend\n      b = (y = new Big(y)).c,\n      // divisor\n      k = x.s == y.s ? 1 : -1,\n      dp = Big.DP;\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\n      throw Error(INVALID_DP);\n    }\n\n    // Divisor is zero?\n    if (!b[0]) {\n      throw Error(DIV_BY_ZERO);\n    }\n\n    // Dividend is 0? Return +-0.\n    if (!a[0]) {\n      y.s = k;\n      y.c = [y.e = 0];\n      return y;\n    }\n    var bl,\n      bt,\n      n,\n      cmp,\n      ri,\n      bz = b.slice(),\n      ai = bl = b.length,\n      al = a.length,\n      r = a.slice(0, bl),\n      // remainder\n      rl = r.length,\n      q = y,\n      // quotient\n      qc = q.c = [],\n      qi = 0,\n      p = dp + (q.e = x.e - y.e) + 1; // precision of the result\n\n    q.s = k;\n    k = p < 0 ? 0 : p;\n\n    // Create version of divisor with leading zero.\n    bz.unshift(0);\n\n    // Add zeros to make remainder as long as divisor.\n    for (; rl++ < bl;) r.push(0);\n    do {\n      // n is how many times the divisor goes into current remainder.\n      for (n = 0; n < 10; n++) {\n        // Compare divisor and remainder.\n        if (bl != (rl = r.length)) {\n          cmp = bl > rl ? 1 : -1;\n        } else {\n          for (ri = -1, cmp = 0; ++ri < bl;) {\n            if (b[ri] != r[ri]) {\n              cmp = b[ri] > r[ri] ? 1 : -1;\n              break;\n            }\n          }\n        }\n\n        // If divisor < remainder, subtract divisor from remainder.\n        if (cmp < 0) {\n          // Remainder can't be more than 1 digit longer than divisor.\n          // Equalise lengths using divisor with extra leading zero?\n          for (bt = rl == bl ? b : bz; rl;) {\n            if (r[--rl] < bt[rl]) {\n              ri = rl;\n              for (; ri && !r[--ri];) r[ri] = 9;\n              --r[ri];\n              r[rl] += 10;\n            }\n            r[rl] -= bt[rl];\n          }\n          for (; !r[0];) r.shift();\n        } else {\n          break;\n        }\n      }\n\n      // Add the digit n to the result array.\n      qc[qi++] = cmp ? n : ++n;\n\n      // Update the remainder.\n      if (r[0] && cmp) r[rl] = a[ai] || 0;else r = [a[ai]];\n    } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\n\n    // Leading zero? Do not remove if result is simply zero (qi == 1).\n    if (!qc[0] && qi != 1) {\n      // There can't be more than one zero.\n      qc.shift();\n      q.e--;\n      p--;\n    }\n\n    // Round?\n    if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\n    return q;\n  };\n\n  /*\r\n   * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n   */\n  P.eq = function (y) {\n    return this.cmp(y) === 0;\n  };\n\n  /*\r\n   * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n   * false.\r\n   */\n  P.gt = function (y) {\n    return this.cmp(y) > 0;\n  };\n\n  /*\r\n   * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n   * return false.\r\n   */\n  P.gte = function (y) {\n    return this.cmp(y) > -1;\n  };\n\n  /*\r\n   * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n   */\n  P.lt = function (y) {\n    return this.cmp(y) < 0;\n  };\n\n  /*\r\n   * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n   * return false.\r\n   */\n  P.lte = function (y) {\n    return this.cmp(y) < 1;\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n   */\n  P.minus = P.sub = function (y) {\n    var i,\n      j,\n      t,\n      xlty,\n      x = this,\n      Big = x.constructor,\n      a = x.s,\n      b = (y = new Big(y)).s;\n\n    // Signs differ?\n    if (a != b) {\n      y.s = -b;\n      return x.plus(y);\n    }\n    var xc = x.c.slice(),\n      xe = x.e,\n      yc = y.c,\n      ye = y.e;\n\n    // Either zero?\n    if (!xc[0] || !yc[0]) {\n      if (yc[0]) {\n        y.s = -b;\n      } else if (xc[0]) {\n        y = new Big(x);\n      } else {\n        y.s = 1;\n      }\n      return y;\n    }\n\n    // Determine which is the bigger number. Prepend zeros to equalise exponents.\n    if (a = xe - ye) {\n      if (xlty = a < 0) {\n        a = -a;\n        t = xc;\n      } else {\n        ye = xe;\n        t = yc;\n      }\n      t.reverse();\n      for (b = a; b--;) t.push(0);\n      t.reverse();\n    } else {\n      // Exponents equal. Check digit by digit.\n      j = ((xlty = xc.length < yc.length) ? xc : yc).length;\n      for (a = b = 0; b < j; b++) {\n        if (xc[b] != yc[b]) {\n          xlty = xc[b] < yc[b];\n          break;\n        }\n      }\n    }\n\n    // x < y? Point xc to the array of the bigger number.\n    if (xlty) {\n      t = xc;\n      xc = yc;\n      yc = t;\n      y.s = -y.s;\n    }\n\n    /*\r\n     * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n     * needs to start at yc.length.\r\n     */\n    if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\n\n    // Subtract yc from xc.\n    for (b = i; j > a;) {\n      if (xc[--j] < yc[j]) {\n        for (i = j; i && !xc[--i];) xc[i] = 9;\n        --xc[i];\n        xc[j] += 10;\n      }\n      xc[j] -= yc[j];\n    }\n\n    // Remove trailing zeros.\n    for (; xc[--b] === 0;) xc.pop();\n\n    // Remove leading zeros and adjust exponent accordingly.\n    for (; xc[0] === 0;) {\n      xc.shift();\n      --ye;\n    }\n    if (!xc[0]) {\n      // n - n = +0\n      y.s = 1;\n\n      // Result must be zero.\n      xc = [ye = 0];\n    }\n    y.c = xc;\n    y.e = ye;\n    return y;\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n   */\n  P.mod = function (y) {\n    var ygtx,\n      x = this,\n      Big = x.constructor,\n      a = x.s,\n      b = (y = new Big(y)).s;\n    if (!y.c[0]) {\n      throw Error(DIV_BY_ZERO);\n    }\n    x.s = y.s = 1;\n    ygtx = y.cmp(x) == 1;\n    x.s = a;\n    y.s = b;\n    if (ygtx) return new Big(x);\n    a = Big.DP;\n    b = Big.RM;\n    Big.DP = Big.RM = 0;\n    x = x.div(y);\n    Big.DP = a;\n    Big.RM = b;\n    return this.minus(x.times(y));\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big negated.\r\n   */\n  P.neg = function () {\n    var x = new this.constructor(this);\n    x.s = -x.s;\n    return x;\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n   */\n  P.plus = P.add = function (y) {\n    var e,\n      k,\n      t,\n      x = this,\n      Big = x.constructor;\n    y = new Big(y);\n\n    // Signs differ?\n    if (x.s != y.s) {\n      y.s = -y.s;\n      return x.minus(y);\n    }\n    var xe = x.e,\n      xc = x.c,\n      ye = y.e,\n      yc = y.c;\n\n    // Either zero?\n    if (!xc[0] || !yc[0]) {\n      if (!yc[0]) {\n        if (xc[0]) {\n          y = new Big(x);\n        } else {\n          y.s = x.s;\n        }\n      }\n      return y;\n    }\n    xc = xc.slice();\n\n    // Prepend zeros to equalise exponents.\n    // Note: reverse faster than unshifts.\n    if (e = xe - ye) {\n      if (e > 0) {\n        ye = xe;\n        t = yc;\n      } else {\n        e = -e;\n        t = xc;\n      }\n      t.reverse();\n      for (; e--;) t.push(0);\n      t.reverse();\n    }\n\n    // Point xc to the longer array.\n    if (xc.length - yc.length < 0) {\n      t = yc;\n      yc = xc;\n      xc = t;\n    }\n    e = yc.length;\n\n    // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\n    for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\n\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n\n    if (k) {\n      xc.unshift(k);\n      ++ye;\n    }\n\n    // Remove trailing zeros.\n    for (e = xc.length; xc[--e] === 0;) xc.pop();\n    y.c = xc;\n    y.e = ye;\n    return y;\n  };\n\n  /*\r\n   * Return a Big whose value is the value of this Big raised to the power n.\r\n   * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n   * mode Big.RM.\r\n   *\r\n   * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n   */\n  P.pow = function (n) {\n    var x = this,\n      one = new x.constructor('1'),\n      y = one,\n      isneg = n < 0;\n    if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\n      throw Error(INVALID + 'exponent');\n    }\n    if (isneg) n = -n;\n    for (;;) {\n      if (n & 1) y = y.times(x);\n      n >>= 1;\n      if (!n) break;\n      x = x.times(x);\n    }\n    return isneg ? one.div(y) : y;\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n   * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n   *\r\n   * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n   * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   */\n  P.prec = function (sd, rm) {\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\n      throw Error(INVALID + 'precision');\n    }\n    return round(new this.constructor(this), sd, rm);\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n   * using rounding mode rm, or Big.RM if rm is not specified.\r\n   * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n   * If dp is not specified, round to 0 decimal places.\r\n   *\r\n   * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n   * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   */\n  P.round = function (dp, rm) {\n    if (dp === UNDEFINED) dp = 0;else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\n      throw Error(INVALID_DP);\n    }\n    return round(new this.constructor(this), dp + this.e + 1, rm);\n  };\n\n  /*\r\n   * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n   * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n   */\n  P.sqrt = function () {\n    var r,\n      c,\n      t,\n      x = this,\n      Big = x.constructor,\n      s = x.s,\n      e = x.e,\n      half = new Big('0.5');\n\n    // Zero?\n    if (!x.c[0]) return new Big(x);\n\n    // Negative?\n    if (s < 0) {\n      throw Error(NAME + 'No square root');\n    }\n\n    // Estimate.\n    s = Math.sqrt(+stringify(x, true, true));\n\n    // Math.sqrt underflow/overflow?\n    // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\n    if (s === 0 || s === 1 / 0) {\n      c = x.c.join('');\n      if (!(c.length + e & 1)) c += '0';\n      s = Math.sqrt(c);\n      e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\n      r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\n    } else {\n      r = new Big(s + '');\n    }\n    e = r.e + (Big.DP += 4);\n\n    // Newton-Raphson iteration.\n    do {\n      t = r;\n      r = half.times(t.plus(x.div(t)));\n    } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\n    return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\n  };\n\n  /*\r\n   * Return a new Big whose value is the value of this Big times the value of Big y.\r\n   */\n  P.times = P.mul = function (y) {\n    var c,\n      x = this,\n      Big = x.constructor,\n      xc = x.c,\n      yc = (y = new Big(y)).c,\n      a = xc.length,\n      b = yc.length,\n      i = x.e,\n      j = y.e;\n\n    // Determine sign of result.\n    y.s = x.s == y.s ? 1 : -1;\n\n    // Return signed 0 if either 0.\n    if (!xc[0] || !yc[0]) {\n      y.c = [y.e = 0];\n      return y;\n    }\n\n    // Initialise exponent of result as x.e + y.e.\n    y.e = i + j;\n\n    // If array xc has fewer digits than yc, swap xc and yc, and lengths.\n    if (a < b) {\n      c = xc;\n      xc = yc;\n      yc = c;\n      j = a;\n      a = b;\n      b = j;\n    }\n\n    // Initialise coefficient array of result with zeros.\n    for (c = new Array(j = a + b); j--;) c[j] = 0;\n\n    // Multiply.\n\n    // i is initially xc.length.\n    for (i = b; i--;) {\n      b = 0;\n\n      // a is yc.length.\n      for (j = a + i; j > i;) {\n        // Current sum of products at this digit position, plus carry.\n        b = c[j] + yc[i] * xc[j - i - 1] + b;\n        c[j--] = b % 10;\n\n        // carry\n        b = b / 10 | 0;\n      }\n      c[j] = b;\n    }\n\n    // Increment result exponent if there is a final carry, otherwise remove leading zero.\n    if (b) ++y.e;else c.shift();\n\n    // Remove trailing zeros.\n    for (i = c.length; !c[--i];) c.pop();\n    y.c = c;\n    return y;\n  };\n\n  /*\r\n   * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n   * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n   *\r\n   * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n   * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   */\n  P.toExponential = function (dp, rm) {\n    var x = this,\n      n = x.c[0];\n    if (dp !== UNDEFINED) {\n      if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\n        throw Error(INVALID_DP);\n      }\n      x = round(new x.constructor(x), ++dp, rm);\n      for (; x.c.length < dp;) x.c.push(0);\n    }\n    return stringify(x, true, !!n);\n  };\n\n  /*\r\n   * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n   * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n   *\r\n   * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n   * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   *\r\n   * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n   * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n   */\n  P.toFixed = function (dp, rm) {\n    var x = this,\n      n = x.c[0];\n    if (dp !== UNDEFINED) {\n      if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\n        throw Error(INVALID_DP);\n      }\n      x = round(new x.constructor(x), dp + x.e + 1, rm);\n\n      // x.e may have changed if the value is rounded up.\n      for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\n    }\n    return stringify(x, false, !!n);\n  };\n\n  /*\r\n   * Return a string representing the value of this Big.\r\n   * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n   * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n   * Omit the sign for negative zero.\r\n   */\n  P.toJSON = P.toString = function () {\n    var x = this,\n      Big = x.constructor;\n    return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\n  };\n\n  /*\r\n   * Return the value of this Big as a primitve number.\r\n   */\n  P.toNumber = function () {\n    var n = +stringify(this, true, true);\n    if (this.constructor.strict === true && !this.eq(n.toString())) {\n      throw Error(NAME + 'Imprecise conversion');\n    }\n    return n;\n  };\n\n  /*\r\n   * Return a string representing the value of this Big rounded to sd significant digits using\r\n   * rounding mode rm, or Big.RM if rm is not specified.\r\n   * Use exponential notation if sd is less than the number of digits necessary to represent\r\n   * the integer part of the value in normal notation.\r\n   *\r\n   * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n   * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n   */\n  P.toPrecision = function (sd, rm) {\n    var x = this,\n      Big = x.constructor,\n      n = x.c[0];\n    if (sd !== UNDEFINED) {\n      if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\n        throw Error(INVALID + 'precision');\n      }\n      x = round(new Big(x), sd, rm);\n      for (; x.c.length < sd;) x.c.push(0);\n    }\n    return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\n  };\n\n  /*\r\n   * Return a string representing the value of this Big.\r\n   * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n   * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n   * Include the sign for negative zero.\r\n   */\n  P.valueOf = function () {\n    var x = this,\n      Big = x.constructor;\n    if (Big.strict === true) {\n      throw Error(NAME + 'valueOf disallowed');\n    }\n    return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\n  };\n\n  // Export\n\n  Big = _Big_();\n  Big['default'] = Big.Big = Big;\n\n  //AMD.\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      return Big;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n    // Node and other CommonJS-like environments that support module.exports.\n  } else {}\n})(this);\n\n//# sourceURL=webpack:///./node_modules/big.js/big.js?");

/***/ }),

/***/ "./src/assets/images/app-bgimg-01.png":
/*!********************************************!*\
  !*** ./src/assets/images/app-bgimg-01.png ***!
  \********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("module.exports = __webpack_require__.p + \"static/img/app-bgimg-01.0279bb59.png\";\n\n//# sourceURL=webpack:///./src/assets/images/app-bgimg-01.png?");

/***/ })

}]);